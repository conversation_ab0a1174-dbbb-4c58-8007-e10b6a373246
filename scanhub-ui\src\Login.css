body {
    min-height: 100vh;
    align-items: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
}

.login-container {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.login-header {
    text-align: center;
    /* position: fixed; */
    top: 7rem;
    left: 0;
    right: 0;
    z-index: 1;

}

.login-body {
    padding: 2rem;
}

.login-header .logo-scan, .login-header .logo-hub {
    font-size: 1.5rem !important;
    font-weight: 600;
}


.login-header .logo-hub {
    color: #fff;
    padding: 0 8px;
    border-radius: 4px;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
}

.btn-continue {
    color: #fff;
    font-weight: 500;
    padding: 10px;
}

.btn-continue:hover {
    background-color: #005f85;
    color: #d0d7dd;
}

.login-header .logo {
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 2rem;
    font-weight: 400;
    color: #000;
    text-align: center;
}

.form-control {
    background-color: #f9f9f9;
}

.login-header .logo img {
    width: 2rem;
    height: 2rem;
    margin-right: 10px;
    margin-bottom: 0.2rem;
}
@media (max-width: 1444px) {
    .login-header{
        top: 0.5rem;
    }
}

@media (max-width: 768px) {

    .info {
        font-size: 1.25rem;
    }

    .sub-info {
        font-size: 1rem;
    }

    .login-header {
        padding: 1.5rem;
        top: 1rem;
    }

    .login-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .login-header {
        padding: 5rem;
    }

    .login-body {
        padding: 1rem;
    }

    .logo {
        font-size: 1.5rem;
    }
}