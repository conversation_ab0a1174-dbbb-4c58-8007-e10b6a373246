import React, { useEffect, useState, useContext } from 'react';
import './Facilities.css';
import img from '../assets/image1.jpg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBookmark, faSlash } from '@fortawesome/free-solid-svg-icons';
import globalContext from '../GlobalContext';
import { useNavigate,useLocation } from 'react-router-dom';
import { getFacilitiesByUser, updateFacilityAsFavorite } from '../services/common.services';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { logout } from '../services/authentication.service';

const Facilities = () => {
  const GlobalContext = useContext(globalContext);
  const MySwal = withReactContent(Swal);
  const location = useLocation();
  let navigate = useNavigate();
  const [lisfOfAllFacilities, setListOfAllFacilities] = useState([]);
  const [isMyFaciliitesTabActive, setIsMyFaciliitesTabActive] = useState(true);
  useEffect(() => {
    fetchData();
    setIsMyFaciliitesTabActive(location.state?location.state.isMyFaciliitesTabActive:true);
  }, []);

  const fetchData = async () => {
    try {
      GlobalContext.startSpinner();
      await getFacilitiesByUser().then((resp) => {
        setListOfAllFacilities(resp.data ? resp.data : []);
        GlobalContext.stopSpinner();
      }).catch((error) => {
        const status = error.response ? error.response.status : null;
        if (status === 403) { navigate('/forbidden'); }
        if (status === 401) { alert('session expired'); logout(); navigate('/'); }
        console.log('Failed to fetch data: ' + error);
        GlobalContext.stopSpinner();
      });
    } catch (er) {
      console.error('Error fetching items: ', er);
    }
  };

  const makeAsMyFacilityAsFavorite = async (item, flag) => {
    try {
      GlobalContext.startSpinner();
      let request = {
        Param1: String(item.Facility_ID)
      };
      await updateFacilityAsFavorite(request).then((resp) => {
        fetchData();
        GlobalContext.stopSpinner();
        MySwal.fire(`${flag ? 'Favorited!' : 'Unfvourited!'}`, `Your facility has been made as ${flag ? ' your favourite' : ' unfavourite'}.`, 'success');
      }).catch((error) => {
        const status = error.response ? error.response.status : null;
        if (status === 403) { navigate('/forbidden'); }
        if (status === 401) { alert('session expired'); logout(); navigate('/'); }
        MySwal.fire('Error!', 'Something went wrong!', 'error');
        GlobalContext.stopSpinner();
      });
    } catch (er) {
      console.error('Error fetching items: ', er);
    }
  };


  const handleDelete = (item, flag) => {
    MySwal.fire({
      title: 'Are you sure?',
      text: `You want to make this facility as ${flag ? ' your favourite' : ' unfavourite'}.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: `Yes, make it ${flag ? ' my favourite!' : ' unfavourite!'}`,
    }).then((result) => {
      if (result.isConfirmed) {
        makeAsMyFacilityAsFavorite(item, flag);
      }
    });
  };

  const navigateToUnites = (item,flag) => {
    setIsMyFaciliitesTabActive(flag);
    navigate('/units', {
      state: { facilityObj: item,isMyFaciliitesTabActive: flag,isPinndedTabActive:true,selectedUnits:[] }
    });
  }


  return (
    <div className="col-lg-12 desktop-container p-0 d-flex flex-column mt-5">
      <div className="text-center p-2 border-bottom fw-bold mt-5">Choose Facility</div>
      <ul className="nav nav-tabs bg-white" id="facilitiesTab" role="tablist">
        <li className="nav-item" role="presentation">
          <button className={`nav-link ${isMyFaciliitesTabActive && 'active'}`} id="my-facilities-tab" data-bs-toggle="tab"
            data-bs-target="#my-facilities-tab-pane" type="button" role="tab" aria-controls="my-facilities-tab-pane"
            aria-selected="true">My Facilities</button>
        </li>
        <li className="nav-item" role="presentation">
          <button className={`nav-link ${!isMyFaciliitesTabActive && 'active'}`} id="all-facilities-tab" data-bs-toggle="tab"
            data-bs-target="#all-facilities-tab-pane" type="button" role="tab" aria-controls="all-facilities-tab-pane"
            aria-selected="false">All Facilities</button>
        </li>
      </ul>
      <div className="tab-content py-3 facility-list custom-bg-light-gray" id="facilitiesTabContent">
        <div className={`tab-pane fade ${isMyFaciliitesTabActive && 'show active'}`} id="my-facilities-tab-pane" role="tabpanel"
          aria-labelledby="my-facilities-tab" tabIndex="0">
          <div className="row g-3">

            {lisfOfAllFacilities.filter(x => x.Is_Favorite).map((item, index) => (
              <div className="col-12 col-sm-6 col-md-4 col-lg-3" id={index} key={item.Facility_ID}>
                <div className="facility d-flex justify-content-between align-items-center border rounded-3 p-3 bg-white h-100">
                  <a className='text-dark text-decoration-none' onClick={()=>navigateToUnites(item,true)}>
                    <div className="facility-details flex-grow-1 me-2">
                      <h4 className="m-0 fs-6">{item.Facility_Name}</h4>
                      <p className="m-0 mt-1 small text-secondary">Patients: {item.Patient_Count}</p>
                    </div>
                  </a>
                  <div className="d-flex align-items-center">
                    <img src={img} alt="facility" />
                    <span className="pin custom-color-blue">
                      <FontAwesomeIcon onClick={() => handleDelete(item, false)} icon={faBookmark} />
                    </span>
                  </div>
                </div>
              </div>
            ))}

          </div>
        </div>
        <div className={`tab-pane fade ${!isMyFaciliitesTabActive && 'show active'}`} id="all-facilities-tab-pane" role="tabpanel" aria-labelledby="all-facilities-tab"
          tabIndex="0">
          <div className="row g-3">

            {lisfOfAllFacilities.map((item, index) => (
              <div className="col-12 col-sm-6 col-md-4 col-lg-3" id={index} key={item.Facility_ID}>
                <div className="facility d-flex justify-content-between align-items-center border rounded-3 p-3 bg-white h-100">
                  <a className='text-dark text-decoration-none' onClick={()=>navigateToUnites(item,false)}>
                    <div className="facility-details flex-grow-1 me-2">
                      <h4 className="m-0 fs-6">{item.Facility_Name}</h4>
                      <p className="m-0 mt-1 small text-secondary">Patients: {item.Patient_Count}</p>
                    </div>
                  </a>
                  <div className="d-flex align-items-center">
                    <img src={img} alt="facility" />
                    <span className="pin custom-color-blue">
                      {item.Is_Favorite ?
                        <FontAwesomeIcon onClick={() => handleDelete(item, false)} icon={faBookmark} />
                        :
                        <>
                          <FontAwesomeIcon icon={faBookmark} />
                          <FontAwesomeIcon onClick={() => handleDelete(item, true)}
                            icon={faSlash}
                            style={{
                              position: 'absolute',
                              marginLeft: '-20px',
                              transform: 'rotate(20deg)',
                            }}
                          />
                        </>
                      }
                    </span>
                  </div>
                </div>
              </div>
            ))}

          </div>
        </div>
      </div>
    </div>
  );
};
export default Facilities;